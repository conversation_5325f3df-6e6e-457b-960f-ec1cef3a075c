#!/usr/bin/env python3
import rospy
#from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import time
import torch
import argparse
from std_msgs.msg import String,Header
import json
import os
import argparse
import os
import sys
import numpy as np
import json
import torch
import time

# 替换原来的 Image 导入为：
from PIL import Image as PILImage  # 图像处理
from sensor_msgs.msg import Image as ROSImage  # ROS 消息

from cv_bridge import CvBridge
# Add GroundingDINO and SAM to path
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
sys.path.append(os.path.join(os.getcwd(), "segment_anything"))

project_root = "/home/<USER>/Grounded-Segment-Anything/ram/recognize-anything-main/"
sys.path.append(project_root)
# RAM imports
from ram.models import ram
from ram import inference_ram as inference
from ram import get_transform as get_ram_transform

# Grounding DINO imports
import GroundingDINO.groundingdino.datasets.transforms as T
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from GroundingDINO.groundingdino.util.inference import predict, annotate

# SAM imports
from segment_anything import (
    sam_model_registry,
    sam_hq_model_registry,
    SamPredictor
)
import cv2
import matplotlib.pyplot as plt

# 在文件顶部添加全局变量和标签集
FILTER_LABELS = {
    'wall', 'tile', 'floor', 'carpet', 'cabinet', 'closet', 'file cabinet', 
    'bed', 'dresser', 'chair', 'armchair', 'swivel chair', 'couch', 'table', 
    'side table', 'round table', 'kitchen table', 'door', 'glass door', 
    'window', 'bookshelf', 'shelf', 'picture', 'picture frame', 'poster', 
    'counter top', 'kitchen counter', 'computer desk', 'office desk', 
    'curtain', 'fridge', 'shower curtain', 'toilet bowl', 'sink', 
    'bathroom sink', 'bath', 'tub', 'bin'
}

# 全局变量存储标签历史
label_history = []
frame_count = 0

def load_image(image_path):
    # load image
    image_pil = PILImage.open(image_path).convert("RGB")  # load image

    transform = T.Compose(
        [
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
        ]
    )
    image, _ = transform(image_pil, None)  # 3, h, w
    return image_pil, image

def load_grounding_model(model_config_path, model_checkpoint_path, bert_base_uncased_path, device):
    args = SLConfig.fromfile(model_config_path)
    args.device = device
    args.bert_base_uncased_path = bert_base_uncased_path
    model = build_model(args)
    checkpoint = torch.load(model_checkpoint_path, map_location="cpu")
    load_res = model.load_state_dict(clean_state_dict(checkpoint["model"]), strict=False)
    print(load_res)
    _ = model.eval()
    return model

def get_grounding_output(model, image, caption, box_threshold, text_threshold, with_logits=True, device="cpu"):
    caption = caption.lower()
    caption = caption.strip()
    if not caption.endswith("."):
        caption = caption + "."
    model = model.to(device)
    image = image.to(device)
    with torch.no_grad():
        outputs = model(image[None], captions=[caption])
    logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
    boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)
    logits.shape[0]

    # filter output
    logits_filt = logits.clone()
    boxes_filt = boxes.clone()
    filt_mask = logits_filt.max(dim=1)[0] > box_threshold
    logits_filt = logits_filt[filt_mask]  # num_filt, 256
    boxes_filt = boxes_filt[filt_mask]  # num_filt, 4
    logits_filt.shape[0]

    # get phrase
    tokenlizer = model.tokenizer
    tokenized = tokenlizer(caption)
    # build pred
    pred_phrases = []
    for logit, box in zip(logits_filt, boxes_filt):
        pred_phrase = get_phrases_from_posmap(logit > text_threshold, tokenized, tokenlizer)
        if with_logits:
            pred_phrases.append(pred_phrase + f"({str(logit.max().item())[:4]})")
        else:
            pred_phrases.append(pred_phrase)

    return boxes_filt, pred_phrases

def show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_box(box, ax, label):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))
    ax.text(x0, y0, label)

def save_mask_data(output_dir, mask_list, box_list, label_list):
    value = 0  # 0 for background

    mask_img = torch.zeros(mask_list.shape[-2:])
    for idx, mask in enumerate(mask_list):
        mask_img[mask.cpu().numpy()[0] == True] = value + idx + 1
    plt.figure(figsize=(10, 10))
    plt.imshow(mask_img.numpy())
    plt.axis('off')
    plt.savefig(os.path.join(output_dir, 'mask.jpg'), bbox_inches="tight", dpi=300, pad_inches=0.0)

    json_data = [{
        'value': value,
        'label': 'background'
    }]
    for label, box in zip(label_list, box_list):
        value += 1
        if '(' in label and label.endswith(')'):
            name, logit = label.split('(')
            logit = logit[:-1]  # 移除末尾的 ')'
            json_data.append({
                'value': value,
                'label': name,
                'logit': float(logit),
                'box': box.numpy().tolist(),
            })
        else:
            json_data.append({
                'value': value,
                'label': label,
                'box': box.numpy().tolist(),
            })
    with open(os.path.join(output_dir, 'mask.json'), 'w') as f:
        json.dump(json_data, f)

def initialize_models(args, device):
    """Initialize all models and return them in a dictionary"""
    models = {}
    
    # Initialize RAM
    print("\n[1/4] Initializing RAM model...")
    start_time = time.time()
    ram_transform = get_ram_transform(image_size=args.ram_image_size)
    ram_model = ram(pretrained=args.ram_pretrained,
                   image_size=args.ram_image_size,
                   vit='swin_l')
    if not hasattr(ram_model, 'forward'):
        raise RuntimeError("RAM模型未正确初始化")
    print(f"RAM模型结构:\n{ram_model}")
    ram_model.eval()
    ram_model = ram_model.to(device)
    models['ram'] = ram_model
    models['ram_transform'] = ram_transform
    print(f"RAM initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize GroundingDINO
    print("\n[2/4] Initializing GroundingDINO model...")
    start_time = time.time()
    grounding_model = load_grounding_model(args.config, args.grounded_checkpoint, 
                                         args.bert_base_uncased_path, device)
    models['dino'] = grounding_model
    print(f"GroundingDINO initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize SAM
    print("\n[3/4] Initializing SAM model...")
    start_time = time.time()
    if args.use_sam_hq:
        sam = sam_hq_model_registry[args.sam_version](checkpoint=args.sam_hq_checkpoint).to(device)
    else:
        sam = sam_model_registry[args.sam_version](checkpoint=args.sam_checkpoint).to(device)
    predictor = SamPredictor(sam)
    models['sam'] = predictor
    print(f"SAM initialization time: {time.time() - start_time:.2f} seconds")
    
    return models

def run_ram(models, image_path, device):
    global label_history, frame_count
    
    print("\n[1/3] Running RAM for tagging...")
    start_time = time.time()
    
    try:
        # 原有的图像处理逻辑
        image_pil = PILImage.open(image_path).convert('RGB')
        if image_pil.size[0] < 10 or image_pil.size[1] < 10:
            raise ValueError("图像尺寸过小")

        image_tensor = models['ram_transform'](image_pil).unsqueeze(0).to(device)
        
        with torch.no_grad():
            tags = inference(image_tensor, models['ram'])
            if tags is None or len(tags) != 2:
                raise RuntimeError(f"推理返回无效结果: {tags}")
            
            ram_tags_en, ram_tags_zh = tags

        # 处理当前帧标签
        current_tags = set(tag.strip() for tag in ram_tags_en.split('|'))
        frame_count += 1
        
        # 添加到历史记录
        label_history.append(current_tags)
        
        # 保持最多5帧历史
        if len(label_history) > 5:
            label_history.pop(0)
        
        # 计算标签并集
        if frame_count <= 5:
            # 前5帧：直接使用当前帧标签
            union_tags = current_tags
        else:
            # 第6帧及以后：与前5帧取并集
            union_tags = set()
            for frame_tags in label_history:
                union_tags.update(frame_tags)
        
        # 与筛选标签集取交集
        filtered_tags = union_tags.intersection(FILTER_LABELS)
        
        # 重新组合标签字符串
        final_tags_en = ' | '.join(sorted(filtered_tags)) if filtered_tags else "object"
        
        print(f"帧 {frame_count} - 原始标签: {ram_tags_en}")
        print(f"筛选后标签: {final_tags_en}")
        
        return final_tags_en, ram_tags_zh, time.time() - start_time

    except Exception as e:
        print(f"❌ RAM处理失败: {str(e)}")
        return "object", "物体", 0.0

# def run_ram(models, image_path, device):
#     """Run RAM model to get tags"""
#     print("\n[1/3] Running RAM for tagging...")
#     start_time = time.time()
    
def run_grounding_dino(models, image_path, ram_tags_en, args, device):
    """
    用新的 GroundingDINO 检测流程（效果更好版本）
    """

    print("\n[2/3] Running GroundingDINO for detection...")
    start_time = time.time()
    # 1) 用 RAM 结果构造更好的 prompt
    text_prompt = ram_tags_en.replace(" | ", ". ") + "."

    # 2) 加载图像
    image_source, image = load_image(image_path)

    # 3) 调用 predict（和你的更好版一致）
    boxes, logits, phrases = predict(
        model=models['dino'],
        image=image,
        caption=text_prompt,
        box_threshold=args.box_threshold,
        text_threshold=args.text_threshold,
        device=device,
    )

    # 4) 确保 phrases 包含置信度信息（模仿 get_grounding_output 的逻辑）
    pred_phrases = []
    for phrase, logit in zip(phrases, logits):
        # 如果 phrase 已经是 "name(score)" 格式，则直接使用
        if '(' in phrase and phrase.endswith(')'):
            pred_phrases.append(phrase)
        else:
            # 否则，添加置信度信息（保留小数点后4位）
            pred_phrases.append(f"{phrase}({logit.max().item():.4f})")

    # 5) 保存检测结果到 output_results
    os.makedirs("./output_results", exist_ok=True)
    detection_output_path = "./output_results/detection_" + os.path.basename(image_path)

    image_source_np = np.array(image_source)
    annotated_image = annotate(
        image_source=image_source_np,
        boxes=boxes,
        logits=logits,
        phrases=pred_phrases  # 使用带置信度的版本
    )
    cv2.imwrite(detection_output_path, annotated_image)
    print(f"Detection results saved to: {detection_output_path}")
    dino_time = time.time() - start_time
    print(f"GroundingDINO processing time: {dino_time:.2f} seconds")
    
    # 6) 返回 GroundingDINO 的输出用于后续 SAM
    return image_source, boxes, pred_phrases, dino_time


def run_sam(models, image_path, boxes_filt, pred_phrases, args, device):
    """Run SAM model for segmentation"""
    print("\n[3/3] Running SAM for segmentation...")
    start_time = time.time()
    
    # Process image with SAM
    image_cv2 = cv2.imread(image_path)
    image_cv2 = cv2.cvtColor(image_cv2, cv2.COLOR_BGR2RGB)
    models['sam'].set_image(image_cv2)
    
    # Transform boxes
    size = PILImage.open(image_path).size
    H, W = size[1], size[0]
    for i in range(boxes_filt.size(0)):
        boxes_filt[i] = boxes_filt[i] * torch.Tensor([W, H, W, H])
        boxes_filt[i][:2] -= boxes_filt[i][2:] / 2
        boxes_filt[i][2:] += boxes_filt[i][:2]
    
    boxes_filt = boxes_filt.cpu()
    transformed_boxes = models['sam'].transform.apply_boxes_torch(boxes_filt, image_cv2.shape[:2]).to(device)
    
    # Predict masks
    masks, _, _ = models['sam'].predict_torch(
        point_coords=None,
        point_labels=None,
        boxes=transformed_boxes.to(device),
        multimask_output=False,
    )
    
    sam_time = time.time() - start_time
    print(f"SAM processing time: {sam_time:.2f} seconds")
    
    return image_cv2, masks, boxes_filt, pred_phrases, sam_time

# def save_results(image_cv2, masks, boxes_filt, pred_phrases, output_dir):
#     """Save all results"""
#     # Draw output image
#     plt.figure(figsize=(10, 10))
#     plt.imshow(image_cv2)
#     for mask in masks:
#         show_mask(mask.cpu().numpy(), plt.gca(), random_color=True)
#     for box, label in zip(boxes_filt, pred_phrases):
#         show_box(box.numpy(), plt.gca(), label)
    
#     plt.axis('off')
#     plt.savefig(
#         os.path.join(output_dir, "ram_grounded_sam_output.jpg"),
#         bbox_inches="tight", dpi=300, pad_inches=0.0
#     )
    
#     # Save mask data
#     save_mask_data(output_dir, masks, boxes_filt, pred_phrases)
# class MyCustomMessage:
#     def __init__(self, header=None):
#         """
#         :param header: std_msgs/Header 消息头（可选）
#         """
#         self.header = header if header else Header()
#         self.detections = []  # 存储检测结果
#         #self.processing_time = rospy.Time.now()
    
#     def add_detection(self, value, label, box=None, logit=None):
#         """添加单个检测结果"""
#         detection = {
#             'value': int(value),
#             'label': str(label)
#         }
#         if box is not None:
#             detection['box'] = [float(x) for x in box]
#         if logit is not None:
#             detection['logit'] = float(logit)
#         self.detections.append(detection)
    
#     def to_dict(self):
#         """转换为字典结构（用于JSON序列化）"""
#         return {
#             'header': {
#                 'stamp': {
#                     'sec': self.header.stamp.secs,
#                     'nsec': self.header.stamp.nsecs
#                 },
#                 'frame_id': self.header.frame_id
#             },
#             'detections': self.detections
#         }
    
#     def to_json_string(self):
#         """生成JSON字符串"""
#         return json.dumps(self.to_dict(), indent=2)
class ROSProcessor:
    def __init__(self, args):
        self.args = args
        self.bridge = CvBridge()
        self.device = torch.device(args.device)
        self.models = initialize_models(args, self.device)
        
        # 初始化ROS节点
        rospy.init_node('ram_groundedsam_node', anonymous=True)
        
        # 订阅图像话题
        self.image_sub = rospy.Subscriber("/camera/rgb/image_raw", ROSImage, self.image_callback)
        #/camera/color/image_raw   /r1/front_camera/image_raw 车/camera/rgb/image_raw
        # 创建发布者ca
        self.result_pub = rospy.Publisher('/processed_image', ROSImage, queue_size=1)
        self.mask_pub = rospy.Publisher('/mask_image', ROSImage, queue_size=1)
        self.mask_json_pub = rospy.Publisher('/mask_data', String, queue_size=1)
        
        rospy.loginfo("RAM+GroundedSAM节点已启动，等待图像数据...")

    def generate_and_publish_masks(self, image_cv2, masks, boxes, labels, original_header, ram_tags_en):
        """
        生成掩码图并发布到ROS话题（包含header信息）
        """
        try:
            # 1-6. 原有的可视化和掩码发布逻辑保持不变
            vis_img = image_cv2.copy()
            mask_img = torch.zeros(masks.shape[-2:], dtype=torch.uint8)
            for idx, mask in enumerate(masks):
                mask_img[mask.cpu().numpy()[0]] = idx + 1
        
            colored_mask = cv2.applyColorMap(mask_img.numpy() * 30, cv2.COLORMAP_JET)
            overlay = cv2.addWeighted(vis_img, 0.5, colored_mask, 0.5, 0)
        
            for box, label in zip(boxes, labels):
                x1, y1 = int(box[0]), int(box[1])
                x2, y2 = int(box[2]), int(box[3])
                cv2.rectangle(overlay, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(overlay, label, (x1, y1-10), 
                cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
        
            result_msg = self.bridge.cv2_to_imgmsg(overlay, "bgr8")
            result_msg.header = original_header
            self.result_pub.publish(result_msg)
        
            mask_msg = self.bridge.cv2_to_imgmsg(mask_img.numpy(), "mono8")
            mask_msg.header = original_header
            self.mask_pub.publish(mask_msg)
        
            # 7. 构建包含header的JSON格式
            json_data = {
                "header": {
                    "stamp": {
                        "sec": original_header.stamp.secs,
                        "nsec": original_header.stamp.nsecs
                    },
                    "frame_id": original_header.frame_id
                },
                "raw_tags": ram_tags_en,
                "tags": ". ".join([label.split('(')[0] for label in labels]),
                "mask": [{"value": 0, "label": "background"}]
            }
        
            # 8. 添加检测对象
            for idx, (label, box) in enumerate(zip(labels, boxes)):
                if '(' in label and label.endswith(')'):
                    name, logit = label.split('(')
                    logit = float(logit[:-1])
                    item = {
                        "value": idx + 1,
                        "labels": {name: logit},
                        "box": box.numpy().tolist() if torch.is_tensor(box) else box
                    }
                else:
                    item = {
                        "value": idx + 1,
                        "labels": {label: 1.0},
                        "box": box.numpy().tolist() if torch.is_tensor(box) else box
                    }
                json_data["mask"].append(item)
        
            # 9. 发布包含header的JSON数据
            json_msg = String()
            json_msg.data = json.dumps(json_data)
            self.mask_json_pub.publish(json_msg)
        
        except Exception as e:
            rospy.logerr(f"掩码可视化失败: {str(e)}")
    def _generate_mask_json(self, labels, boxes):
        """生成与save_mask_data相同的JSON结构"""
        json_data = [{'value': 0, 'label': 'background'}]
        for idx, (label, box) in enumerate(zip(labels, boxes)):
            item = {
                'value': idx + 1,
                'label': label.split('(')[0] if '(' in label else label,
                'box': box.numpy().tolist() if torch.is_tensor(box) else box
            }
            if '(' in label and label.endswith(')'):
                item['logit'] = float(label.split('(')[1][:-1])
            json_data.append(item)
        return json_data
    
    def image_callback(self, msg):
        try:
            start_time = time.time()
            original_header = msg.header
            
            # 1-4. 处理流程保持不变
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            resized_image = cv2.resize(cv_image, (640, 480))
            temp_path = os.path.join(self.args.output_dir, "temp_ros.jpg")
            cv2.imwrite(temp_path, resized_image)
            
            ram_tags_en, _, ram_time = run_ram(self.models, temp_path, self.device)
            _, boxes_filt, pred_phrases, dino_time = run_grounding_dino(
                self.models, temp_path, ram_tags_en, self.args, self.device
            )
            image_cv2, masks, boxes_filt, pred_phrases, sam_time = run_sam(
                self.models, temp_path, boxes_filt, pred_phrases, self.args, self.device
            )
            
            # 5. 发布结果（不保存文件）
            self.generate_and_publish_masks(image_cv2, masks, boxes_filt, pred_phrases, original_header, ram_tags_en)
            
            # 6. 记录处理时间
            total_time = time.time() - start_time
            rospy.loginfo(f"处理完成 - 总时间: {total_time:.2f}s (RAM: {ram_time:.2f}s | DINO: {dino_time:.2f}s | SAM: {sam_time:.2f}s)")
        
        except Exception as e:
            rospy.logerr(f"图像处理失败: {str(e)}")
    def save_frame_data(self, output_dir, frame_id, masks, boxes, labels, ram_tags_en):
        """保存为指定格式的JSON和PNG文件"""
        
        # 1. 生成掩码图像
        mask_img = torch.zeros(masks.shape[-2:], dtype=torch.uint8)
        for idx, mask in enumerate(masks):
            mask_img[mask.cpu().numpy()[0]] = idx + 1
        
        # 2. 保存PNG掩码文件
        mask_path = os.path.join(output_dir, f"frame-{frame_id:06d}_mask.png")
        cv2.imwrite(mask_path, mask_img.numpy())
        
        # 3. 构建JSON数据
        json_data = {
            "raw_tags": ram_tags_en,
            "tags": ". ".join([label.split('(')[0] for label in labels]),
            "mask": [{"value": 0, "label": "background"}]
        }
        
        # 4. 添加检测对象
        for idx, (label, box) in enumerate(zip(labels, boxes)):
            if '(' in label and label.endswith(')'):
                name, logit = label.split('(')
                logit = float(logit[:-1])
                item = {
                    "value": idx + 1,
                    "labels": {name: logit},
                    "box": box.numpy().tolist()
                }
            else:
                item = {
                    "value": idx + 1,
                    "labels": {label: 1.0},
                    "box": box.numpy().tolist()
                }
            json_data["mask"].append(item)
        
        # 5. 保存JSON文件
        json_path = os.path.join(output_dir, f"frame-{frame_id:06d}_label.json")
        with open(json_path, 'w') as f:
            json.dump(json_data, f)
def process_image_file(args):#处理单个文件
    """处理单个图像文件的原有功能"""
    total_start_time = time.time()
    
    # Make output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize device
    device = torch.device(args.device)
    
    # ==================== Model Initialization ====================
    init_start_time = time.time()
    models = initialize_models(args, device)
    init_time = time.time() - init_start_time
    print(f"\nTotal model initialization time: {init_time:.2f} seconds")
    
    # Save raw image
    PILImage.open(args.image).save(os.path.join(args.output_dir, "raw_image.jpg"))
    
    # ==================== RAM Processing ====================
    ram_tags_en, ram_tags_zh, ram_time = run_ram(models, args.image, device)
    text_prompt = ram_tags_en
    
    # ==================== GroundingDINO Processing ====================
    image_pil_gs, boxes_filt, pred_phrases, dino_time = run_grounding_dino(
        models, args.image, text_prompt, args, device
    )
    
    # ==================== SAM Processing ====================
    image_cv2, masks, boxes_filt, pred_phrases, sam_time = run_sam(
        models, args.image, boxes_filt, pred_phrases, args, device
    )
    
    # ==================== Save Results ====================
    save_results(image_cv2, masks, boxes_filt, pred_phrases, args.output_dir)
    
    # ==================== Print Timing Summary ====================
    total_time = time.time() - total_start_time
    print("\n========== Timing Summary ==========")
    print(f"Model Initialization Time: {init_time:.2f} seconds")
    print(f"RAM Processing Time:      {ram_time:.2f} seconds")
    print(f"GroundingDINO Time:       {dino_time:.2f} seconds")
    print(f"SAM Processing Time:      {sam_time:.2f} seconds")
    print("-----------------------------------")
    print(f"Total Processing Time:    {total_time:.2f} seconds")
    print("===================================")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser("RAM + Grounded-Segment-Anything Demo", add_help=True)
    
    # RAM arguments
    parser.add_argument("--image", type=str, default=None, help="path to image file (optional if using ROS)")
    parser.add_argument("--ram_pretrained", type=str, default="/home/<USER>/Grounded-Segment-Anything/ram/recognize-anything-main/pretrained/ram_swin_large_14m.pth", 
                       help="path to RAM pretrained model")
    parser.add_argument("--ram_image_size", type=int, default=384, help="RAM input image size")
    
    # GroundedSAM arguments
    parser.add_argument("--config", type=str, default="GroundingDINO/groundingdino/config/GroundingDINO_SwinT_OGC.py", 
                       help="path to GroundingDINO config file")
    parser.add_argument("--grounded_checkpoint", type=str, default="groundingdino_swint_ogc.pth", 
                       help="path to GroundingDINO checkpoint")
    parser.add_argument("--sam_version", type=str, default="vit_b", help="SAM ViT version: vit_b / vit_l / vit_h")
    parser.add_argument("--sam_checkpoint", type=str, default="sam_vit_b_01ec64.pth", 
                       help="path to SAM checkpoint")
    parser.add_argument("--sam_hq_checkpoint", type=str, default=None, help="path to SAM-HQ checkpoint")
    parser.add_argument("--use_sam_hq", action="store_true", help="use SAM-HQ model")
    parser.add_argument("--output_dir", "-o", type=str, default="output_results", help="output directory")
    parser.add_argument("--box_threshold", type=float, default=0.3, help="box threshold")
    parser.add_argument("--text_threshold", type=float, default=0.25, help="text threshold")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", 
                       help="running device (default: cuda if available else cpu)")
    parser.add_argument("--bert_base_uncased_path", type=str, help="path to bert base uncased model")
    parser.add_argument("--ros", action="store_true", help="enable ROS mode")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.image is not None:
        # 文件模式 - 处理单个图像
        process_image_file(args)
    elif args.ros:
        # ROS模式
        processor = ROSProcessor(args)
        rospy.spin()
    else:
        print("请指定 --image 参数提供图像路径，或使用 --ros 参数启用ROS模式")

if __name__ == "__main__":
    main()
