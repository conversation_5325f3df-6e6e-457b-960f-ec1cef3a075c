--meta_arch motr
--dataset_file e2e_dance
--epoch 5
--with_box_refine
--lr_drop 4
--lr 2e-4
--lr_backbone 2e-5
--pretrained /mnt/dolphinfs/hdd_pool/docker/user/hadoop-vacv/yanfeng/project/MOTRv2/MOTRv3/checkpoints/r50_deformable_detr_plus_iterative_bbox_refinement-checkpoint.pth
--batch_size 1
--sample_mode random_interval
--sample_interval 10
--sampler_lengths 5
--merger_dropout 0
--dropout 0
--random_drop 0.1
--fp_ratio 0.3
--query_interaction_layer QIMv2
--query_denoise 0.05
--num_queries 10
--append_crowd
--det_db det_db_motrv2.json
--use_checkpoint
--mot_path /home/<USER>/yanfeng/data
