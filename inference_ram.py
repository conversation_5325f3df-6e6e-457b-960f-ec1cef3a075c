'''
 * The Recognize Anything Model (RAM)
 * Written by <PERSON><PERSON><PERSON>
'''
import argparse
import numpy as np
import random
from collections import deque

import torch
import time

start = time.time()
from PIL import Image
from ram.models import ram
from ram import inference_ram as inference
from ram import get_transform

global all_valid_tags

LO_LABELS = {
    'wall', 'tile', 'wall', 'floor', 'carpet', 
    'cabinet', 'closet', 'file cabinet', 'bed', 
    'dresser', 'chair', 'armchair', 'swivel chair', 
    'couch', 'table', 'side table', 'round table', 
    'kitchen table', 'door', 'glass door', 'window', 
    'bookshelf', 'shelf', 'picture', 'picture' 'frame', 
    'poster', 'counter top', 'kitchen' 'counter', 
    'computer' 'desk', 'office desk, curtain', 
    'fridge', 'shower curtain', 'toilet bowl', 
    'sink', 'bathroom sink', 'bath', 'tub', 'bin'
}

class LabelAugmenter:
    def __init__(self, window_size=5):
        self.window_size = window_size
        self.label_history = deque(maxlen=window_size)
    
    def process_frame_labels(self, current_labels):
        """
        Process labels for current frame:
        1. Intersect with LO_LABELS
        2. Union with labels from previous frames
        """
        # Convert current frame labels to set (assuming English output)
        current_tags = set(tag.strip() for tag in current_labels[0].split('|'))
        
        # Step 1: Intersection with LO_LABELS
        valid_tags = current_tags.intersection(LO_LABELS)
        all_valid_tags.update(valid_tags)
        # Add current valid tags to history
        self.label_history.append(valid_tags)
        
        # Step 2: Union with previous frames' labels
        augmented_tags = set()
        for frame_tags in self.label_history:
            augmented_tags.update(frame_tags)
        
        # Convert back to RAM's output format
        augmented_output = ('|'.join(augmented_tags), '|'.join(augmented_tags))
        
        return augmented_output
    
parser = argparse.ArgumentParser(
    description='Tag2Text inferece for tagging and captioning')
parser.add_argument('--images',
                    nargs='+',  # 接受多个输入
                    metavar='DIR',
                    help='paths to multiple images',
                    default=['./images/enhance/01.jpg', './images/enhance/02.jpg', './images/enhance/03.jpg', './images/enhance/04.jpg', './images/enhance/05.jpg'])
parser.add_argument('--pretrained',
                    metavar='DIR',
                    help='path to pretrained model',
                    default='./pretrained/ram_swin_large_14m.pth')
parser.add_argument('--image-size',
                    default=384,
                    type=int,
                    metavar='N',
                    help='input image size (default: 384)')


if __name__ == "__main__":
    args = parser.parse_args()
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    transform = get_transform(image_size=args.image_size)

    # 加载模型
    model = ram(pretrained=args.pretrained,
                image_size=args.image_size,
                vit='swin_l')
    model.eval()
    model = model.to(device)

    # 初始化标签增强器
    label_augmenter = LabelAugmenter(window_size=5)
    all_valid_tags = set()
    # 处理每一帧
    for image_path in args.images:
        image = transform(Image.open(image_path)).unsqueeze(0).to(device)
        raw_output = inference(image, model)
        augmented_output = label_augmenter.process_frame_labels(raw_output)
        
        print(f"\nImage: {image_path}")
        print("Raw Tags: ", raw_output[0])
        print("Augmented Tags: ", augmented_output[0])
    print("\nFinal Augmented Tags:", "|".join(all_valid_tags))
    end = time.time()
    print("Total time = ", end - start)