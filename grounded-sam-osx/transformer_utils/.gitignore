

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
**/*.pyc

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/en/_build
docs/zh_cn/_build

# PyBuilder
target/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/

# custom
mmpose/.mim
/models
/data
.vscode
.idea
*.pkl
*.pkl.json
*.log.json
*.npy
work_dirs/
docs/**/topics/
docs/**/papers/*.md
docs/**/datasets.md
docs/**/modelzoo.md

!tests/data/**/*.pkl
!tests/data/**/*.pkl.json
!tests/data/**/*.log.json
!tests/data/**/*.pth
!tests/data/**/*.npy

# Pytorch
*.pth

*.DS_Store

# checkpoints
ckpts/
vis_results
vis_results_poseur
scripts