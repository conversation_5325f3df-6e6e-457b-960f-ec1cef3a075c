[bdist_wheel]
universal=1

[aliases]
test=pytest

[tool:pytest]
addopts=tests/

[yapf]
based_on_style = pep8
blank_line_before_nested_class_or_def = true
split_before_expression_after_opening_paren = true
split_penalty_import_names=0
SPLIT_PENALTY_AFTER_OPENING_BRACKET=800

[isort]
line_length = 79
multi_line_output = 0
extra_standard_library = pkg_resources,setuptools
known_first_party = mmpose
known_third_party = PIL,cv2,h5py,json_tricks,matplotlib,mmcv,munkres,numpy,pytest,pytorch_sphinx_theme,requests,scipy,seaborn,spacepy,titlecase,torch,torchvision,webcam_apis,xmltodict,xtcocotools
no_lines_before = STDLIB,LOCALFOLDER
default_section = THIRDPARTY
