dataset_info = dict(
    dataset_name='aflw',
    paper_info=dict(
        author='<PERSON><PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON> and '
        '<PERSON>, <PERSON> and <PERSON><PERSON>, <PERSON><PERSON>',
        title='Annotated facial landmarks in the wild: '
        'A large-scale, real-world database for facial '
        'landmark localization',
        container='2011 IEEE international conference on computer '
        'vision workshops (ICCV workshops)',
        year='2011',
        homepage='https://www.tugraz.at/institute/icg/research/'
        'team-bischof/lrs/downloads/aflw/',
    ),
    keypoint_info={
        0:
        dict(name='kpt-0', id=0, color=[255, 255, 255], type='', swap='kpt-5'),
        1:
        dict(name='kpt-1', id=1, color=[255, 255, 255], type='', swap='kpt-4'),
        2:
        dict(name='kpt-2', id=2, color=[255, 255, 255], type='', swap='kpt-3'),
        3:
        dict(name='kpt-3', id=3, color=[255, 255, 255], type='', swap='kpt-2'),
        4:
        dict(name='kpt-4', id=4, color=[255, 255, 255], type='', swap='kpt-1'),
        5:
        dict(name='kpt-5', id=5, color=[255, 255, 255], type='', swap='kpt-0'),
        6:
        dict(
            name='kpt-6', id=6, color=[255, 255, 255], type='', swap='kpt-11'),
        7:
        dict(
            name='kpt-7', id=7, color=[255, 255, 255], type='', swap='kpt-10'),
        8:
        dict(name='kpt-8', id=8, color=[255, 255, 255], type='', swap='kpt-9'),
        9:
        dict(name='kpt-9', id=9, color=[255, 255, 255], type='', swap='kpt-8'),
        10:
        dict(
            name='kpt-10', id=10, color=[255, 255, 255], type='',
            swap='kpt-7'),
        11:
        dict(
            name='kpt-11', id=11, color=[255, 255, 255], type='',
            swap='kpt-6'),
        12:
        dict(
            name='kpt-12',
            id=12,
            color=[255, 255, 255],
            type='',
            swap='kpt-14'),
        13:
        dict(name='kpt-13', id=13, color=[255, 255, 255], type='', swap=''),
        14:
        dict(
            name='kpt-14',
            id=14,
            color=[255, 255, 255],
            type='',
            swap='kpt-12'),
        15:
        dict(
            name='kpt-15',
            id=15,
            color=[255, 255, 255],
            type='',
            swap='kpt-17'),
        16:
        dict(name='kpt-16', id=16, color=[255, 255, 255], type='', swap=''),
        17:
        dict(
            name='kpt-17',
            id=17,
            color=[255, 255, 255],
            type='',
            swap='kpt-15'),
        18:
        dict(name='kpt-18', id=18, color=[255, 255, 255], type='', swap='')
    },
    skeleton_info={},
    joint_weights=[1.] * 19,
    sigmas=[])
