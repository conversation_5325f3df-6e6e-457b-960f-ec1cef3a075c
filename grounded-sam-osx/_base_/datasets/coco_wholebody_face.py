dataset_info = dict(
    dataset_name='coco_wholebody_face',
    paper_info=dict(
        author='<PERSON>, <PERSON><PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON>, Jin and '
        '<PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and '
        '<PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON>',
        title='Whole-Body Human Pose Estimation in the Wild',
        container='Proceedings of the European '
        'Conference on Computer Vision (ECCV)',
        year='2020',
        homepage='https://github.com/jin-s13/COCO-WholeBody/',
    ),
    keypoint_info={
        0:
        dict(
            name='face-0',
            id=0,
            color=[255, 255, 255],
            type='',
            swap='face-16'),
        1:
        dict(
            name='face-1',
            id=1,
            color=[255, 255, 255],
            type='',
            swap='face-15'),
        2:
        dict(
            name='face-2',
            id=2,
            color=[255, 255, 255],
            type='',
            swap='face-14'),
        3:
        dict(
            name='face-3',
            id=3,
            color=[255, 255, 255],
            type='',
            swap='face-13'),
        4:
        dict(
            name='face-4',
            id=4,
            color=[255, 255, 255],
            type='',
            swap='face-12'),
        5:
        dict(
            name='face-5',
            id=5,
            color=[255, 255, 255],
            type='',
            swap='face-11'),
        6:
        dict(
            name='face-6',
            id=6,
            color=[255, 255, 255],
            type='',
            swap='face-10'),
        7:
        dict(
            name='face-7', id=7, color=[255, 255, 255], type='',
            swap='face-9'),
        8:
        dict(name='face-8', id=8, color=[255, 255, 255], type='', swap=''),
        9:
        dict(
            name='face-9', id=9, color=[255, 255, 255], type='',
            swap='face-7'),
        10:
        dict(
            name='face-10',
            id=10,
            color=[255, 255, 255],
            type='',
            swap='face-6'),
        11:
        dict(
            name='face-11',
            id=11,
            color=[255, 255, 255],
            type='',
            swap='face-5'),
        12:
        dict(
            name='face-12',
            id=12,
            color=[255, 255, 255],
            type='',
            swap='face-4'),
        13:
        dict(
            name='face-13',
            id=13,
            color=[255, 255, 255],
            type='',
            swap='face-3'),
        14:
        dict(
            name='face-14',
            id=14,
            color=[255, 255, 255],
            type='',
            swap='face-2'),
        15:
        dict(
            name='face-15',
            id=15,
            color=[255, 255, 255],
            type='',
            swap='face-1'),
        16:
        dict(
            name='face-16',
            id=16,
            color=[255, 255, 255],
            type='',
            swap='face-0'),
        17:
        dict(
            name='face-17',
            id=17,
            color=[255, 255, 255],
            type='',
            swap='face-26'),
        18:
        dict(
            name='face-18',
            id=18,
            color=[255, 255, 255],
            type='',
            swap='face-25'),
        19:
        dict(
            name='face-19',
            id=19,
            color=[255, 255, 255],
            type='',
            swap='face-24'),
        20:
        dict(
            name='face-20',
            id=20,
            color=[255, 255, 255],
            type='',
            swap='face-23'),
        21:
        dict(
            name='face-21',
            id=21,
            color=[255, 255, 255],
            type='',
            swap='face-22'),
        22:
        dict(
            name='face-22',
            id=22,
            color=[255, 255, 255],
            type='',
            swap='face-21'),
        23:
        dict(
            name='face-23',
            id=23,
            color=[255, 255, 255],
            type='',
            swap='face-20'),
        24:
        dict(
            name='face-24',
            id=24,
            color=[255, 255, 255],
            type='',
            swap='face-19'),
        25:
        dict(
            name='face-25',
            id=25,
            color=[255, 255, 255],
            type='',
            swap='face-18'),
        26:
        dict(
            name='face-26',
            id=26,
            color=[255, 255, 255],
            type='',
            swap='face-17'),
        27:
        dict(name='face-27', id=27, color=[255, 255, 255], type='', swap=''),
        28:
        dict(name='face-28', id=28, color=[255, 255, 255], type='', swap=''),
        29:
        dict(name='face-29', id=29, color=[255, 255, 255], type='', swap=''),
        30:
        dict(name='face-30', id=30, color=[255, 255, 255], type='', swap=''),
        31:
        dict(
            name='face-31',
            id=31,
            color=[255, 255, 255],
            type='',
            swap='face-35'),
        32:
        dict(
            name='face-32',
            id=32,
            color=[255, 255, 255],
            type='',
            swap='face-34'),
        33:
        dict(name='face-33', id=33, color=[255, 255, 255], type='', swap=''),
        34:
        dict(
            name='face-34',
            id=34,
            color=[255, 255, 255],
            type='',
            swap='face-32'),
        35:
        dict(
            name='face-35',
            id=35,
            color=[255, 255, 255],
            type='',
            swap='face-31'),
        36:
        dict(
            name='face-36',
            id=36,
            color=[255, 255, 255],
            type='',
            swap='face-45'),
        37:
        dict(
            name='face-37',
            id=37,
            color=[255, 255, 255],
            type='',
            swap='face-44'),
        38:
        dict(
            name='face-38',
            id=38,
            color=[255, 255, 255],
            type='',
            swap='face-43'),
        39:
        dict(
            name='face-39',
            id=39,
            color=[255, 255, 255],
            type='',
            swap='face-42'),
        40:
        dict(
            name='face-40',
            id=40,
            color=[255, 255, 255],
            type='',
            swap='face-47'),
        41:
        dict(
            name='face-41',
            id=41,
            color=[255, 255, 255],
            type='',
            swap='face-46'),
        42:
        dict(
            name='face-42',
            id=42,
            color=[255, 255, 255],
            type='',
            swap='face-39'),
        43:
        dict(
            name='face-43',
            id=43,
            color=[255, 255, 255],
            type='',
            swap='face-38'),
        44:
        dict(
            name='face-44',
            id=44,
            color=[255, 255, 255],
            type='',
            swap='face-37'),
        45:
        dict(
            name='face-45',
            id=45,
            color=[255, 255, 255],
            type='',
            swap='face-36'),
        46:
        dict(
            name='face-46',
            id=46,
            color=[255, 255, 255],
            type='',
            swap='face-41'),
        47:
        dict(
            name='face-47',
            id=47,
            color=[255, 255, 255],
            type='',
            swap='face-40'),
        48:
        dict(
            name='face-48',
            id=48,
            color=[255, 255, 255],
            type='',
            swap='face-54'),
        49:
        dict(
            name='face-49',
            id=49,
            color=[255, 255, 255],
            type='',
            swap='face-53'),
        50:
        dict(
            name='face-50',
            id=50,
            color=[255, 255, 255],
            type='',
            swap='face-52'),
        51:
        dict(name='face-51', id=52, color=[255, 255, 255], type='', swap=''),
        52:
        dict(
            name='face-52',
            id=52,
            color=[255, 255, 255],
            type='',
            swap='face-50'),
        53:
        dict(
            name='face-53',
            id=53,
            color=[255, 255, 255],
            type='',
            swap='face-49'),
        54:
        dict(
            name='face-54',
            id=54,
            color=[255, 255, 255],
            type='',
            swap='face-48'),
        55:
        dict(
            name='face-55',
            id=55,
            color=[255, 255, 255],
            type='',
            swap='face-59'),
        56:
        dict(
            name='face-56',
            id=56,
            color=[255, 255, 255],
            type='',
            swap='face-58'),
        57:
        dict(name='face-57', id=57, color=[255, 255, 255], type='', swap=''),
        58:
        dict(
            name='face-58',
            id=58,
            color=[255, 255, 255],
            type='',
            swap='face-56'),
        59:
        dict(
            name='face-59',
            id=59,
            color=[255, 255, 255],
            type='',
            swap='face-55'),
        60:
        dict(
            name='face-60',
            id=60,
            color=[255, 255, 255],
            type='',
            swap='face-64'),
        61:
        dict(
            name='face-61',
            id=61,
            color=[255, 255, 255],
            type='',
            swap='face-63'),
        62:
        dict(name='face-62', id=62, color=[255, 255, 255], type='', swap=''),
        63:
        dict(
            name='face-63',
            id=63,
            color=[255, 255, 255],
            type='',
            swap='face-61'),
        64:
        dict(
            name='face-64',
            id=64,
            color=[255, 255, 255],
            type='',
            swap='face-60'),
        65:
        dict(
            name='face-65',
            id=65,
            color=[255, 255, 255],
            type='',
            swap='face-67'),
        66:
        dict(name='face-66', id=66, color=[255, 255, 255], type='', swap=''),
        67:
        dict(
            name='face-67',
            id=67,
            color=[255, 255, 255],
            type='',
            swap='face-65')
    },
    skeleton_info={},
    joint_weights=[1.] * 68,

    # 'https://github.com/jin-s13/COCO-WholeBody/blob/master/'
    # 'evaluation/myeval_wholebody.py#L177'
    sigmas=[
        0.042, 0.043, 0.044, 0.043, 0.040, 0.035, 0.031, 0.025, 0.020, 0.023,
        0.029, 0.032, 0.037, 0.038, 0.043, 0.041, 0.045, 0.013, 0.012, 0.011,
        0.011, 0.012, 0.012, 0.011, 0.011, 0.013, 0.015, 0.009, 0.007, 0.007,
        0.007, 0.012, 0.009, 0.008, 0.016, 0.010, 0.017, 0.011, 0.009, 0.011,
        0.009, 0.007, 0.013, 0.008, 0.011, 0.012, 0.010, 0.034, 0.008, 0.008,
        0.009, 0.008, 0.008, 0.007, 0.010, 0.008, 0.009, 0.009, 0.009, 0.007,
        0.007, 0.008, 0.011, 0.008, 0.008, 0.008, 0.01, 0.008
    ])
