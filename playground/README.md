## Playground

We will try more interesting **base models** and **build more fun demos** in the playground. In the playground, we will:

- **Simplify the demo code** to make it easier for users to get started.
- **Keep complete usage notes** and some pitfalls to reduce the burden on users.

## Table of Contents
- [DeepFloyd: Text-to-Image Generation](./DeepFloyd/)
  - [Dream: Text-to-Image Generation](./DeepFloyd/dream.py)
  - [Style Transfer](./DeepFloyd/style_transfer.py)
- [Paint by Example: Exemplar-based Image Editing with Diffusion Models](./PaintByExample/)
  - [Diffuser Demo](./PaintByExample/paint_by_example.py)
  - [PaintByExample with SAM](./PaintByExample/sam_paint_by_example.py)
- [LaMa: Resolution-robust Large Mask Inpainting with Fourier Convolutions](./LaMa/)
  - [LaMa Demo](./LaMa/lama_inpaint_demo.py)
  - [LaMa with SAM](./LaMa/sam_lama.py)
- [RePaint: Inpainting using Denoising Diffusion Probabilistic Models](./RePaint/)
  - [RePaint Demo](./RePaint/repaint.py)
