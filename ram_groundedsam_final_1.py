#!/usr/bin/env python3
import rospy
#from sensor_msgs.msg import Image
from cv_bridge import CvBridge
import time
import torch
import argparse
import os
import argparse
import os
import sys
import numpy as np
import json
import torch
import time
# 替换原来的 Image 导入为：
from PIL import Image as PILImage  # 图像处理
from sensor_msgs.msg import Image as ROSImage  # ROS 消息

from cv_bridge import CvBridge
# Add GroundingDINO and SAM to path
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
sys.path.append(os.path.join(os.getcwd(), "segment_anything"))

project_root = "/home/<USER>/Grounded-Segment-Anything/ram/recognize-anything-main/"
sys.path.append(project_root)
# RAM imports
from ram.models import ram
from ram import inference_ram as inference
from ram import get_transform as get_ram_transform

# Grounding DINO imports
import GroundingDINO.groundingdino.datasets.transforms as T
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from GroundingDINO.groundingdino.util.inference import predict, annotate

# SAM imports
from segment_anything import (
    sam_model_registry,
    sam_hq_model_registry,
    SamPredictor
)
import cv2
import matplotlib.pyplot as plt

def load_image(image_path):
    # load image
    image_pil = PILImage.open(image_path).convert("RGB")  # load image

    transform = T.Compose(
        [
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
        ]
    )
    image, _ = transform(image_pil, None)  # 3, h, w
    return image_pil, image

def load_grounding_model(model_config_path, model_checkpoint_path, bert_base_uncased_path, device):
    args = SLConfig.fromfile(model_config_path)
    args.device = device
    args.bert_base_uncased_path = bert_base_uncased_path
    model = build_model(args)
    checkpoint = torch.load(model_checkpoint_path, map_location="cpu")
    load_res = model.load_state_dict(clean_state_dict(checkpoint["model"]), strict=False)
    print(load_res)
    _ = model.eval()
    return model

def get_grounding_output(model, image, caption, box_threshold, text_threshold, with_logits=True, device="cpu"):
    caption = caption.lower()
    caption = caption.strip()
    if not caption.endswith("."):
        caption = caption + "."
    model = model.to(device)
    image = image.to(device)
    with torch.no_grad():
        outputs = model(image[None], captions=[caption])
    logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
    boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)
    logits.shape[0]

    # filter output
    logits_filt = logits.clone()
    boxes_filt = boxes.clone()
    filt_mask = logits_filt.max(dim=1)[0] > box_threshold
    logits_filt = logits_filt[filt_mask]  # num_filt, 256
    boxes_filt = boxes_filt[filt_mask]  # num_filt, 4
    logits_filt.shape[0]

    # get phrase
    tokenlizer = model.tokenizer
    tokenized = tokenlizer(caption)
    # build pred
    pred_phrases = []
    for logit, box in zip(logits_filt, boxes_filt):
        pred_phrase = get_phrases_from_posmap(logit > text_threshold, tokenized, tokenlizer)
        if with_logits:
            pred_phrases.append(pred_phrase + f"({str(logit.max().item())[:4]})")
        else:
            pred_phrases.append(pred_phrase)

    return boxes_filt, pred_phrases

def show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_box(box, ax, label):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))
    ax.text(x0, y0, label)

def save_mask_data(output_dir, mask_list, box_list, label_list):
    value = 0  # 0 for background

    mask_img = torch.zeros(mask_list.shape[-2:])
    for idx, mask in enumerate(mask_list):
        mask_img[mask.cpu().numpy()[0] == True] = value + idx + 1
    plt.figure(figsize=(10, 10))
    plt.imshow(mask_img.numpy())
    plt.axis('off')
    plt.savefig(os.path.join(output_dir, 'mask.jpg'), bbox_inches="tight", dpi=300, pad_inches=0.0)

    json_data = [{
        'value': value,
        'label': 'background'
    }]
    for label, box in zip(label_list, box_list):
        value += 1
        if '(' in label and label.endswith(')'):
            name, logit = label.split('(')
            logit = logit[:-1]  # 移除末尾的 ')'
            json_data.append({
                'value': value,
                'label': name,
                'logit': float(logit),
                'box': box.numpy().tolist(),
            })
        else:
            json_data.append({
                'value': value,
                'label': label,
                'box': box.numpy().tolist(),
            })
    with open(os.path.join(output_dir, 'mask.json'), 'w') as f:
        json.dump(json_data, f)

def initialize_models(args, device):
    """Initialize all models and return them in a dictionary"""
    models = {}
    
    # Initialize RAM
    print("\n[1/4] Initializing RAM model...")
    start_time = time.time()
    ram_transform = get_ram_transform(image_size=args.ram_image_size)
    ram_model = ram(pretrained=args.ram_pretrained,
                   image_size=args.ram_image_size,
                   vit='swin_l')
    if not hasattr(ram_model, 'forward'):
        raise RuntimeError("RAM模型未正确初始化")
    print(f"RAM模型结构:\n{ram_model}")
    ram_model.eval()
    ram_model = ram_model.to(device)
    models['ram'] = ram_model
    models['ram_transform'] = ram_transform
    print(f"RAM initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize GroundingDINO
    print("\n[2/4] Initializing GroundingDINO model...")
    start_time = time.time()
    grounding_model = load_grounding_model(args.config, args.grounded_checkpoint, 
                                         args.bert_base_uncased_path, device)
    models['dino'] = grounding_model
    print(f"GroundingDINO initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize SAM
    print("\n[3/4] Initializing SAM model...")
    start_time = time.time()
    if args.use_sam_hq:
        sam = sam_hq_model_registry[args.sam_version](checkpoint=args.sam_hq_checkpoint).to(device)
    else:
        sam = sam_model_registry[args.sam_version](checkpoint=args.sam_checkpoint).to(device)
    predictor = SamPredictor(sam)
    models['sam'] = predictor
    print(f"SAM initialization time: {time.time() - start_time:.2f} seconds")
    
    return models

def run_ram(models, image_path, device):
    print("\n[1/3] Running RAM for tagging...")
    start_time = time.time()
    
    try:
        # 1. 加载并严格验证图像
        image_pil = PILImage.open(image_path).convert('RGB')
        if image_pil.size[0] < 10 or image_pil.size[1] < 10:
            raise ValueError("图像尺寸过小")

        # 2. 打印预处理前的图像信息（调试）
        print(f"预处理前 - 尺寸: {image_pil.size}, 模式: {image_pil.mode}")

        # 3. 应用transform并检查输出张量
        image_tensor = models['ram_transform'](image_pil).unsqueeze(0).to(device)
        print(f"张量形状: {image_tensor.shape}, 值范围: [{image_tensor.min():.3f}, {image_tensor.max():.3f}]")

        # 4. 推理时捕获具体错误
        with torch.no_grad():
            tags = inference(image_tensor, models['ram'])  # 先不直接解包
            if tags is None or len(tags) != 2:
                raise RuntimeError(f"推理返回无效结果: {tags}")
            ram_tags_en, ram_tags_zh = tags

        # 5. 验证标签内容
        if not isinstance(ram_tags_en, str) or not isinstance(ram_tags_zh, str):
            raise ValueError(f"标签格式错误: en={type(ram_tags_en)}, zh={type(ram_tags_zh)}")

        print(f"RAM标签 - 英文: {ram_tags_en}\n中文: {ram_tags_zh}")
        return ram_tags_en, ram_tags_zh, time.time() - start_time

    except Exception as e:
        print(f"❌ RAM处理失败: {str(e)}")
        # 返回安全默认值
        return "object", "物体", 0.0

# def run_ram(models, image_path, device):
#     """Run RAM model to get tags"""
#     print("\n[1/3] Running RAM for tagging...")
#     start_time = time.time()
    
def run_grounding_dino(models, image_path, ram_tags_en, args, device):
    """
    用新的 GroundingDINO 检测流程（效果更好版本）
    """

    print("\n[2/3] Running GroundingDINO for detection...")
    start_time = time.time()
    # 1) 用 RAM 结果构造更好的 prompt
    text_prompt = ram_tags_en.replace(" | ", ". ") + "."

    # 2) 加载图像
    image_source, image = load_image(image_path)

    # 3) 调用 predict（和你的更好版一致）
    boxes, logits, phrases = predict(
        model=models['dino'],
        image=image,
        caption=text_prompt,
        box_threshold=args.box_threshold,
        text_threshold=args.text_threshold,
        device=device,
    )

    # 4) 确保 phrases 包含置信度信息（模仿 get_grounding_output 的逻辑）
    pred_phrases = []
    for phrase, logit in zip(phrases, logits):
        # 如果 phrase 已经是 "name(score)" 格式，则直接使用
        if '(' in phrase and phrase.endswith(')'):
            pred_phrases.append(phrase)
        else:
            # 否则，添加置信度信息（保留小数点后4位）
            pred_phrases.append(f"{phrase}({logit.max().item():.4f})")

    # 5) 保存检测结果到 output_results
    os.makedirs("./output_results", exist_ok=True)
    detection_output_path = "./output_results/detection_" + os.path.basename(image_path)

    image_source_np = np.array(image_source)
    annotated_image = annotate(
        image_source=image_source_np,
        boxes=boxes,
        logits=logits,
        phrases=pred_phrases  # 使用带置信度的版本
    )
    cv2.imwrite(detection_output_path, annotated_image)
    print(f"Detection results saved to: {detection_output_path}")
    dino_time = time.time() - start_time
    print(f"GroundingDINO processing time: {dino_time:.2f} seconds")
    
    # 6) 返回 GroundingDINO 的输出用于后续 SAM
    return image_source, boxes, pred_phrases, dino_time


def run_sam(models, image_path, boxes_filt, pred_phrases, args, device):
    """Run SAM model for segmentation"""
    print("\n[3/3] Running SAM for segmentation...")
    start_time = time.time()
    
    # Process image with SAM
    image_cv2 = cv2.imread(image_path)
    image_cv2 = cv2.cvtColor(image_cv2, cv2.COLOR_BGR2RGB)
    models['sam'].set_image(image_cv2)
    
    # Transform boxes
    size = PILImage.open(image_path).size
    H, W = size[1], size[0]
    for i in range(boxes_filt.size(0)):
        boxes_filt[i] = boxes_filt[i] * torch.Tensor([W, H, W, H])
        boxes_filt[i][:2] -= boxes_filt[i][2:] / 2
        boxes_filt[i][2:] += boxes_filt[i][:2]
    
    boxes_filt = boxes_filt.cpu()
    transformed_boxes = models['sam'].transform.apply_boxes_torch(boxes_filt, image_cv2.shape[:2]).to(device)
    
    # Predict masks
    masks, _, _ = models['sam'].predict_torch(
        point_coords=None,
        point_labels=None,
        boxes=transformed_boxes.to(device),
        multimask_output=False,
    )
    
    sam_time = time.time() - start_time
    print(f"SAM processing time: {sam_time:.2f} seconds")
    
    return image_cv2, masks, boxes_filt, pred_phrases, sam_time

def save_results(image_cv2, masks, boxes_filt, pred_phrases, output_dir):
    """Save all results"""
    # Draw output image
    plt.figure(figsize=(10, 10))
    plt.imshow(image_cv2)
    for mask in masks:
        show_mask(mask.cpu().numpy(), plt.gca(), random_color=True)
    for box, label in zip(boxes_filt, pred_phrases):
        show_box(box.numpy(), plt.gca(), label)
    
    plt.axis('off')
    plt.savefig(
        os.path.join(output_dir, "ram_grounded_sam_output.jpg"),
        bbox_inches="tight", dpi=300, pad_inches=0.0
    )
    
    # Save mask data
    save_mask_data(output_dir, masks, boxes_filt, pred_phrases)

class ROSProcessor:
    def __init__(self, args):
        self.args = args
        self.bridge = CvBridge()
        self.device = torch.device(args.device)
        self.models = initialize_models(args, self.device)
        
        # 初始化ROS节点
        rospy.init_node('ram_groundedsam_node', anonymous=True)
        
        # 订阅图像话题
        self.image_sub = rospy.Subscriber("/camera/color/image_raw", ROSImage, self.image_callback)
        
        # 创建发布者（如果需要发布结果）
        self.result_pub = rospy.Publisher('/processed_image', ROSImage, queue_size=1)
        self.mask_pub = rospy.Publisher('/mask_image', ROSImage, queue_size=1)
        rospy.loginfo("RAM+GroundedSAM节点已启动，等待图像数据...")
    def generate_and_publish_masks(self, image_cv2, masks, boxes, labels):
    # """
    # 生成彩色掩码图并发布到ROS话题
    # 参数:
    #     image_cv2: 原始OpenCV图像 (BGR格式)
    #     masks: SAM输出的掩码列表 [N,1,H,W]
    #     boxes: 边界框列表 [N,4]
    #     labels: 标签列表 [N,]
    # """
        try:
            # 1. 创建基础图像（保留原始图像）
            vis_img = image_cv2.copy()
            
            # 2. 生成彩色掩码（与save_mask_data逻辑一致）
            mask_img = torch.zeros(masks.shape[-2:], device='cpu')  # 初始化全黑背景
            
            # 为每个掩码分配唯一颜色值
            for idx, mask in enumerate(masks):
                color_val = idx + 1  # 背景为0，物体从1开始编号
                mask_img[mask.cpu().numpy()[0]] = color_val
            
            # 3. 转换为彩色可视化（类似mask.jpg效果）
            # 3.1 将单通道索引图转为伪彩色
            mask_np = mask_img.numpy().astype(np.uint8)
            colored_mask = cv2.applyColorMap(mask_np * 10, cv2.COLORMAP_JET)  # 放大颜色差异
            
            # 3.2 叠加到原始图像（50%透明度）
            overlay = cv2.addWeighted(vis_img, 0.5, colored_mask, 0.5, 0)
            
            # 4. 绘制边界框和标签（类似ram_grounded_sam_output.jpg效果）
            for box, label in zip(boxes, labels):
                x1, y1, x2, y2 = map(int, box.numpy())
                # 绘制边界框（绿色）
                cv2.rectangle(overlay, (x1, y1), (x2, y2), (0, 255, 0), 2)
                # 绘制标签（红色文字）
                cv2.putText(overlay, label, (x1, y1-10), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # 5. 发布到ROS话题
            ros_msg = self.bridge.cv2_to_imgmsg(overlay, "bgr8")
            self.result_pub.publish(ros_msg)
            
            # 6. 生成JSON数据（可选，如需发布到其他话题）
            json_data = self._generate_mask_json(labels, boxes)
            
        except Exception as e:
            rospy.logerr(f"掩码可视化失败: {str(e)}")

    def _generate_mask_json(self, labels, boxes):
        """生成与save_mask_data相同的JSON结构"""
        json_data = [{'value': 0, 'label': 'background'}]
        for idx, (label, box) in enumerate(zip(labels, boxes)):
            item = {
                'value': idx + 1,
                'label': label.split('(')[0] if '(' in label else label,
                'box': box.numpy().tolist()
            }
            if '(' in label and label.endswith(')'):
                item['logit'] = float(label.split('(')[1][:-1])
            json_data.append(item)
        return json_data
    def image_callback(self, msg):
        try:
            start_time = time.time()
                
            # 转换ROS图像为OpenCV格式
            cv_image = self.bridge.imgmsg_to_cv2(msg, "bgr8")
            # 保存临时文件时检查尺寸
            print(f"收到图像尺寸: {cv_image.shape}")  # 调试输出
            temp_path = os.path.join(self.args.output_dir, "temp_ros.jpg")
            cv2.imwrite(temp_path, cv_image)
            
            
            if not os.path.exists(temp_path):
                raise RuntimeError("临时图像保存失败")
                # 保存临时图像文件（如果需要）
            temp_image_path = os.path.join(self.args.output_dir, "temp_ros_image.jpg")
            cv2.imwrite(temp_image_path, cv_image)
                
                # ==================== RAM Processing ====================
            ram_tags_en, ram_tags_zh, ram_time = run_ram(self.models, temp_image_path, self.device)
            text_prompt = ram_tags_en
                
                # ==================== GroundingDINO Processing ====================
            image_pil_gs, boxes_filt, pred_phrases, dino_time = run_grounding_dino(
                self.models, temp_image_path, text_prompt, self.args, self.device
            )
                
                # ==================== SAM Processing ====================
            image_cv2, masks, boxes_filt, pred_phrases, sam_time = run_sam(
                self.models, temp_image_path, boxes_filt, pred_phrases, self.args, self.device
            )
                
                # ==================== Save Results ====================
            save_results(image_cv2, masks, boxes_filt, pred_phrases, self.args.output_dir)
                
                # 发布处理后的图像（可选）
            #result_msg = self.bridge.cv2_to_imgmsg(image_cv2, "bgr8")
            #self.result_pub.publish(result_msg)
                
                # 打印处理时间
            total_time = time.time() - start_time
            rospy.loginfo(f"处理完成 - 总时间: {total_time:.2f}秒 | RAM: {ram_time:.2f} | DINO: {dino_time:.2f} | SAM: {sam_time:.2f}")
                
        except Exception as e:
            rospy.logerr(f"处理图像时出错: {str(e)}")
    
def process_image_file(args):
    """处理单个图像文件的原有功能"""
    total_start_time = time.time()
    
    # Make output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize device
    device = torch.device(args.device)
    
    # ==================== Model Initialization ====================
    init_start_time = time.time()
    models = initialize_models(args, device)
    init_time = time.time() - init_start_time
    print(f"\nTotal model initialization time: {init_time:.2f} seconds")
    
    # Save raw image
    PILImage.open(args.image).save(os.path.join(args.output_dir, "raw_image.jpg"))
    
    # ==================== RAM Processing ====================
    ram_tags_en, ram_tags_zh, ram_time = run_ram(models, args.image, device)
    text_prompt = ram_tags_en
    
    # ==================== GroundingDINO Processing ====================
    image_pil_gs, boxes_filt, pred_phrases, dino_time = run_grounding_dino(
        models, args.image, text_prompt, args, device
    )
    
    # ==================== SAM Processing ====================
    image_cv2, masks, boxes_filt, pred_phrases, sam_time = run_sam(
        models, args.image, boxes_filt, pred_phrases, args, device
    )
    
    # ==================== Save Results ====================
    save_results(image_cv2, masks, boxes_filt, pred_phrases, args.output_dir)
    
    # ==================== Print Timing Summary ====================
    total_time = time.time() - total_start_time
    print("\n========== Timing Summary ==========")
    print(f"Model Initialization Time: {init_time:.2f} seconds")
    print(f"RAM Processing Time:      {ram_time:.2f} seconds")
    print(f"GroundingDINO Time:       {dino_time:.2f} seconds")
    print(f"SAM Processing Time:      {sam_time:.2f} seconds")
    print("-----------------------------------")
    print(f"Total Processing Time:    {total_time:.2f} seconds")
    print("===================================")

def main():
    # Parse arguments
    parser = argparse.ArgumentParser("RAM + Grounded-Segment-Anything Demo", add_help=True)
    
    # RAM arguments
    parser.add_argument("--image", type=str, default=None, help="path to image file (optional if using ROS)")
    parser.add_argument("--ram_pretrained", type=str, default="/home/<USER>/Grounded-Segment-Anything/ram/recognize-anything-main/pretrained/ram_swin_large_14m.pth", 
                       help="path to RAM pretrained model")
    parser.add_argument("--ram_image_size", type=int, default=384, help="RAM input image size")
    
    # GroundedSAM arguments
    parser.add_argument("--config", type=str, default="GroundingDINO/groundingdino/config/GroundingDINO_SwinT_OGC.py", 
                       help="path to GroundingDINO config file")
    parser.add_argument("--grounded_checkpoint", type=str, default="groundingdino_swint_ogc.pth", 
                       help="path to GroundingDINO checkpoint")
    parser.add_argument("--sam_version", type=str, default="vit_b", help="SAM ViT version: vit_b / vit_l / vit_h")
    parser.add_argument("--sam_checkpoint", type=str, default="sam_vit_b_01ec64.pth", 
                       help="path to SAM checkpoint")
    parser.add_argument("--sam_hq_checkpoint", type=str, default=None, help="path to SAM-HQ checkpoint")
    parser.add_argument("--use_sam_hq", action="store_true", help="use SAM-HQ model")
    parser.add_argument("--output_dir", "-o", type=str, default="output_results", help="output directory")
    parser.add_argument("--box_threshold", type=float, default=0.3, help="box threshold")
    parser.add_argument("--text_threshold", type=float, default=0.25, help="text threshold")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", 
                       help="running device (default: cuda if available else cpu)")
    parser.add_argument("--bert_base_uncased_path", type=str, help="path to bert base uncased model")
    parser.add_argument("--ros", action="store_true", help="enable ROS mode")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    if args.image is not None:
        # 文件模式 - 处理单个图像
        process_image_file(args)
    elif args.ros:
        # ROS模式
        processor = ROSProcessor(args)
        rospy.spin()
    else:
        print("请指定 --image 参数提供图像路径，或使用 --ros 参数启用ROS模式")

if __name__ == "__main__":
    main()