import argparse
import os
import sys
import numpy as np
import json
import torch
import time
from PIL import Image

# Add GroundingDINO and SAM to path
sys.path.append(os.path.join(os.getcwd(), "GroundingDINO"))
sys.path.append(os.path.join(os.getcwd(), "segment_anything"))
from ram.recognize_anything_main.ram.models import ram
# RAM imports
#from ram.models import ram
from ram import inference_ram as inference
from ram import get_transform as get_ram_transform

# Grounding DINO imports
import GroundingDINO.groundingdino.datasets.transforms as T
from GroundingDINO.groundingdino.models import build_model
from GroundingDINO.groundingdino.util.slconfig import SLConfig
from GroundingDINO.groundingdino.util.utils import clean_state_dict, get_phrases_from_posmap
from GroundingDINO.groundingdino.util.inference import predict, annotate

# SAM imports
from segment_anything import (
    sam_model_registry,
    sam_hq_model_registry,
    SamPredictor
)
import cv2
import matplotlib.pyplot as plt

def load_image(image_path):
    # load image
    image_pil = Image.open(image_path).convert("RGB")  # load image

    transform = T.Compose(
        [
            T.RandomResize([800], max_size=1333),
            T.ToTensor(),
            T.Normalize([0.485, 0.456, 0.406], [0.229, 0.224, 0.225]),
        ]
    )
    image, _ = transform(image_pil, None)  # 3, h, w
    return image_pil, image

def load_grounding_model(model_config_path, model_checkpoint_path, bert_base_uncased_path, device):
    args = SLConfig.fromfile(model_config_path)
    args.device = device
    args.bert_base_uncased_path = bert_base_uncased_path
    model = build_model(args)
    checkpoint = torch.load(model_checkpoint_path, map_location="cpu")
    load_res = model.load_state_dict(clean_state_dict(checkpoint["model"]), strict=False)
    print(load_res)
    _ = model.eval()
    return model

def get_grounding_output(model, image, caption, box_threshold, text_threshold, with_logits=True, device="cpu"):
    caption = caption.lower()
    caption = caption.strip()
    if not caption.endswith("."):
        caption = caption + "."
    model = model.to(device)
    image = image.to(device)
    with torch.no_grad():
        outputs = model(image[None], captions=[caption])
    logits = outputs["pred_logits"].cpu().sigmoid()[0]  # (nq, 256)
    boxes = outputs["pred_boxes"].cpu()[0]  # (nq, 4)
    logits.shape[0]

    # filter output
    logits_filt = logits.clone()
    boxes_filt = boxes.clone()
    filt_mask = logits_filt.max(dim=1)[0] > box_threshold
    logits_filt = logits_filt[filt_mask]  # num_filt, 256
    boxes_filt = boxes_filt[filt_mask]  # num_filt, 4
    logits_filt.shape[0]

    # get phrase
    tokenlizer = model.tokenizer
    tokenized = tokenlizer(caption)
    # build pred
    pred_phrases = []
    for logit, box in zip(logits_filt, boxes_filt):
        pred_phrase = get_phrases_from_posmap(logit > text_threshold, tokenized, tokenlizer)
        if with_logits:
            pred_phrases.append(pred_phrase + f"({str(logit.max().item())[:4]})")
        else:
            pred_phrases.append(pred_phrase)

    return boxes_filt, pred_phrases

def show_mask(mask, ax, random_color=False):
    if random_color:
        color = np.concatenate([np.random.random(3), np.array([0.6])], axis=0)
    else:
        color = np.array([30/255, 144/255, 255/255, 0.6])
    h, w = mask.shape[-2:]
    mask_image = mask.reshape(h, w, 1) * color.reshape(1, 1, -1)
    ax.imshow(mask_image)

def show_box(box, ax, label):
    x0, y0 = box[0], box[1]
    w, h = box[2] - box[0], box[3] - box[1]
    ax.add_patch(plt.Rectangle((x0, y0), w, h, edgecolor='green', facecolor=(0,0,0,0), lw=2))
    ax.text(x0, y0, label)

def save_mask_data(output_dir, mask_list, box_list, label_list):
    value = 0  # 0 for background

    mask_img = torch.zeros(mask_list.shape[-2:])
    for idx, mask in enumerate(mask_list):
        mask_img[mask.cpu().numpy()[0] == True] = value + idx + 1
    plt.figure(figsize=(10, 10))
    plt.imshow(mask_img.numpy())
    plt.axis('off')
    plt.savefig(os.path.join(output_dir, 'mask.jpg'), bbox_inches="tight", dpi=300, pad_inches=0.0)

    json_data = [{
        'value': value,
        'label': 'background'
    }]
    for label, box in zip(label_list, box_list):
        value += 1
        name, logit = label.split('(')
        logit = logit[:-1] # the last is ')'
        json_data.append({
            'value': value,
            'label': name,
            'logit': float(logit),
            'box': box.numpy().tolist(),
        })
    with open(os.path.join(output_dir, 'mask.json'), 'w') as f:
        json.dump(json_data, f)

def initialize_models(args, device):
    """Initialize all models and return them in a dictionary"""
    models = {}
    
    # Initialize RAM
    print("\n[1/4] Initializing RAM model...")
    start_time = time.time()
    ram_transform = get_ram_transform(image_size=args.ram_image_size)
    ram_model = ram(pretrained=args.ram_pretrained,
                   image_size=args.ram_image_size,
                   vit='swin_l')
    ram_model.eval()
    ram_model = ram_model.to(device)
    models['ram'] = ram_model
    models['ram_transform'] = ram_transform
    print(f"RAM initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize GroundingDINO
    print("\n[2/4] Initializing GroundingDINO model...")
    start_time = time.time()
    grounding_model = load_grounding_model(args.config, args.grounded_checkpoint, 
                                         args.bert_base_uncased_path, device)
    models['dino'] = grounding_model
    print(f"GroundingDINO initialization time: {time.time() - start_time:.2f} seconds")
    
    # Initialize SAM
    print("\n[3/4] Initializing SAM model...")
    start_time = time.time()
    if args.use_sam_hq:
        sam = sam_hq_model_registry[args.sam_version](checkpoint=args.sam_hq_checkpoint).to(device)
    else:
        sam = sam_model_registry[args.sam_version](checkpoint=args.sam_checkpoint).to(device)
    predictor = SamPredictor(sam)
    models['sam'] = predictor
    print(f"SAM initialization time: {time.time() - start_time:.2f} seconds")
    
    return models

def run_ram(models, image_path, device):
    """Run RAM model to get tags"""
    print("\n[1/3] Running RAM for tagging...")
    start_time = time.time()
    
    # Process image with RAM
    image_pil = Image.open(image_path)
    image_ram = models['ram_transform'](image_pil).unsqueeze(0).to(device)
    
    # Get tags from RAM
    with torch.no_grad():
        ram_tags_en, ram_tags_zh = inference(image_ram, models['ram'])
    
    print("RAM English Tags:", ram_tags_en)
    print("RAM Chinese Tags:", ram_tags_zh)
    
    ram_time = time.time() - start_time
    print(f"RAM processing time: {ram_time:.2f} seconds")
    
    return ram_tags_en, ram_tags_zh, ram_time

def run_grounding_dino(models, image_path, ram_tags_en, args, device):
    """
    用新的 GroundingDINO 检测流程（效果更好版本）
    """

    print("\n[2/3] Running GroundingDINO for detection...")
    start_time = time.time()
    # 1) 用 RAM 结果构造更好的 prompt
    text_prompt = ram_tags_en.replace(" | ", ". ") + "."

    # 2) 加载图像
    image_source, image = load_image(image_path)

    # 3) 调用 predict（和你的更好版一致）
    boxes, logits, phrases = predict(
        model=models['dino'],
        image=image,
        caption=text_prompt,
        box_threshold=args.box_threshold,
        text_threshold=args.text_threshold,
        device=device,
    )

    # 4) 保存检测结果到 output_results
    os.makedirs("./output_results", exist_ok=True)
    detection_output_path = "./output_results/detection_" + os.path.basename(image_path)

    image_source_np = np.array(image_source)
    annotated_image = annotate(
        image_source=image_source_np,
        boxes=boxes,
        logits=logits,
        phrases=phrases
    )
    cv2.imwrite(detection_output_path, annotated_image)
    print(f"Detection results saved to: {detection_output_path}")
    dino_time = time.time() - start_time
    print(f"GroundingDINO processing time: {dino_time:.2f} seconds")
    # 5) 返回 GroundingDINO 的输出用于后续 SAM
    return image_source, boxes, phrases, dino_time



def run_sam(models, image_path, boxes_filt, pred_phrases, args, device):
    """Run SAM model for segmentation"""
    print("\n[3/3] Running SAM for segmentation...")
    start_time = time.time()
    
    # Process image with SAM
    image_cv2 = cv2.imread(image_path)
    image_cv2 = cv2.cvtColor(image_cv2, cv2.COLOR_BGR2RGB)
    models['sam'].set_image(image_cv2)
    
    # Transform boxes
    size = Image.open(image_path).size
    H, W = size[1], size[0]
    for i in range(boxes_filt.size(0)):
        boxes_filt[i] = boxes_filt[i] * torch.Tensor([W, H, W, H])
        boxes_filt[i][:2] -= boxes_filt[i][2:] / 2
        boxes_filt[i][2:] += boxes_filt[i][:2]
    
    boxes_filt = boxes_filt.cpu()
    transformed_boxes = models['sam'].transform.apply_boxes_torch(boxes_filt, image_cv2.shape[:2]).to(device)
    
    # Predict masks
    masks, _, _ = models['sam'].predict_torch(
        point_coords=None,
        point_labels=None,
        boxes=transformed_boxes.to(device),
        multimask_output=False,
    )
    
    sam_time = time.time() - start_time
    print(f"SAM processing time: {sam_time:.2f} seconds")
    
    return image_cv2, masks, boxes_filt, pred_phrases, sam_time

def save_results(image_cv2, masks, boxes_filt, pred_phrases, output_dir):
    """Save all results"""
    # Draw output image
    plt.figure(figsize=(10, 10))
    plt.imshow(image_cv2)
    for mask in masks:
        show_mask(mask.cpu().numpy(), plt.gca(), random_color=True)
    for box, label in zip(boxes_filt, pred_phrases):
        show_box(box.numpy(), plt.gca(), label)
    
    plt.axis('off')
    plt.savefig(
        os.path.join(output_dir, "ram_grounded_sam_output.jpg"),
        bbox_inches="tight", dpi=300, pad_inches=0.0
    )
    
    # Save mask data
    save_mask_data(output_dir, masks, boxes_filt, pred_phrases)

def main():
    total_start_time = time.time()
    
    # Parse arguments
    parser = argparse.ArgumentParser("RAM + Grounded-Segment-Anything Demo", add_help=True)
    
    # RAM arguments
    parser.add_argument("--image", type=str, required=True, help="path to image file")
    parser.add_argument("--ram_pretrained", type=str, default="/home/<USER>/Grounded-Segment-Anything/ram/recognize-anything-main/pretrained/ram_swin_large_14m.pth", 
                        help="path to RAM pretrained model")
    parser.add_argument("--ram_image_size", type=int, default=384, help="RAM input image size")
    
    # GroundedSAM arguments
    parser.add_argument("--config", type=str, required=True, help="path to GroundingDINO config file")
    parser.add_argument("--grounded_checkpoint", type=str, required=True, help="path to GroundingDINO checkpoint")
    parser.add_argument("--sam_version", type=str, default="vit_b", help="SAM ViT version: vit_b / vit_l / vit_h")
    parser.add_argument("--sam_checkpoint", type=str, required=True, help="path to SAM checkpoint")
    parser.add_argument("--sam_hq_checkpoint", type=str, default=None, help="path to SAM-HQ checkpoint")
    parser.add_argument("--use_sam_hq", action="store_true", help="use SAM-HQ model")
    parser.add_argument("--output_dir", "-o", type=str, default="outputs", help="output directory")
    parser.add_argument("--box_threshold", type=float, default=0.3, help="box threshold")
    parser.add_argument("--text_threshold", type=float, default=0.25, help="text threshold")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", 
                        help="running device (default: cuda if available else cpu)")
    parser.add_argument("--bert_base_uncased_path", type=str, help="path to bert base uncased model")
    
    args = parser.parse_args()
    # Make output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize device
    device = torch.device(args.device)
    
    # ==================== Model Initialization ====================
    init_start_time = time.time()
    models = initialize_models(args, device)
    init_time = time.time() - init_start_time
    print(f"\nTotal model initialization time: {init_time:.2f} seconds")
    
    # Save raw image
    Image.open(args.image).save(os.path.join(args.output_dir, "raw_image.jpg"))
    
    # ==================== RAM Processing ====================
    ram_tags_en, ram_tags_zh, ram_time = run_ram(models, args.image, device)
    
    # Use the English tags as prompt for GroundedSAM
    text_prompt = ram_tags_en
    
    # ==================== GroundingDINO Processing ====================
    image_pil_gs, boxes_filt, pred_phrases, dino_time = run_grounding_dino(
        models, args.image, text_prompt, args, device
    )
    
    # ==================== SAM Processing ====================
    image_cv2, masks, boxes_filt, pred_phrases, sam_time = run_sam(
        models, args.image, boxes_filt, pred_phrases, args, device
    )
    
    # ==================== Save Results ====================
    save_results(image_cv2, masks, boxes_filt, pred_phrases, args.output_dir)
    
    # ==================== Print Timing Summary ====================
    total_time = time.time() - total_start_time
    print("\n========== Timing Summary ==========")
    print(f"Model Initialization Time: {init_time:.2f} seconds")
    print(f"RAM Processing Time:      {ram_time:.2f} seconds")
    print(f"GroundingDINO Time:       {dino_time:.2f} seconds")
    print(f"SAM Processing Time:      {sam_time:.2f} seconds")
    print("-----------------------------------")
    print(f"Total Processing Time:    {total_time:.2f} seconds")
    print("===================================")

if __name__ == "__main__":
    main()